import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Button, theme, App } from 'antd';
import {
  DashboardOutlined,
  KeyOutlined,
  FileOutlined,
  AppstoreOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  <PERSON>uUnfoldOutlined,
  LockOutlined,
  Line<PERSON><PERSON>Outlined,
  Bar<PERSON><PERSON>Outlined
} from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { adminLogout } from '../../store/slices/adminSlice';

const { Header, Content, Sider, Footer } = Layout;

const AdminLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { admin } = useSelector((state) => state.admin);
  const { token } = theme.useToken();

  const handleLogout = () => {
    dispatch(adminLogout());
    navigate('/admin/login');
  };

  // 菜单项
  const menuItems = [
    {
      key: '/admin',
      icon: <DashboardOutlined />,
      label: <Link to="/admin">控制台</Link>,
    },
    {
      key: '/admin/data-analytics',
      icon: <BarChartOutlined />,
      label: <Link to="/admin/data-analytics">数据分析</Link>,
    },
    {
      key: '/admin/card-keys',
      icon: <KeyOutlined />,
      label: <Link to="/admin/card-keys">卡密管理</Link>,
    },
    {
      key: '/admin/files',
      icon: <FileOutlined />,
      label: <Link to="/admin/files">文件管理</Link>,
    },
    {
      key: '/admin/extraction-records',
      icon: <LineChartOutlined />,
      label: <Link to="/admin/extraction-records">提取记录</Link>,
    },
    {
      key: '/admin/categories',
      icon: <AppstoreOutlined />,
      label: <Link to="/admin/categories">分类管理</Link>,
    },
  ];

  // 用户下拉菜单项
  const userMenuItems = [
    {
      key: '1',
      label: '返回首页',
      icon: <DashboardOutlined />,
      onClick: () => navigate('/'),
    },
    {
      key: '2',
      label: '修改密码',
      icon: <LockOutlined />,
      onClick: () => navigate('/admin/change-password'),
    },
    {
      key: '3',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        collapsible
        collapsed={collapsed}
        onCollapse={(value) => setCollapsed(value)}
        theme="light"
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 1000
        }}
      >
        <div className="text-center" style={{ margin: '16px 0', fontWeight: 'bold', fontSize: '20px' }}>
          {!collapsed ? '自助提取系统管理' : ''}
        </div>
        <Menu
          theme="light"
          mode="inline"
          defaultSelectedKeys={['/admin']}
          selectedKeys={[window.location.pathname]}
          items={menuItems}
        />
      </Sider>
      <Layout style={{ marginLeft: collapsed ? 80 : 200 }}>
        <Header
          style={{
            padding: 0,
            background: token.colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingLeft: '16px',
            paddingRight: '16px',
            boxShadow: '0 1px 3px rgba(0,0,0,.1)',
            position: 'fixed',
            width: `calc(100% - ${collapsed ? 80 : 200}px)`,
            zIndex: 999,
            top: 0
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
          />
          <div>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
                <Avatar 
                  src="/images/admin-avatar.jpeg" 
                  size="default"
                  style={{ backgroundColor: '#1890ff' }}
                />
                <span style={{ marginLeft: 8 }}>{admin?.username || '管理员'}</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        <Content style={{ margin: '16px', marginTop: 80 }}>
          <div
            style={{
              padding: 24,
              minHeight: 360,
              background: token.colorBgContainer,
              borderRadius: token.borderRadiusLG,
            }}
          >
            <App>{children}</App>
          </div>
        </Content>
        <Footer style={{ textAlign: 'center', padding: '12px' }}>
          自助提取系统 ©{new Date().getFullYear()} 管理后台
        </Footer>
      </Layout>
    </Layout>
  );
};

export default AdminLayout; 
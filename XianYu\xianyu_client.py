#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼商品信息获取客户端

独立的、可复用的闲鱼API客户端，可以轻松集成到任何项目中。

功能：
1. 获取商品列表
2. 获取商品详细信息
3. 自动分页处理
4. 错误处理和重试机制

使用示例：
    from xianyu_client import XianyuClient
    
    client = XianyuClient(cookies_str="your_cookies_here")
    items = client.get_all_items(get_details=True, max_items=10)
    
    for item in items:
        print(f"商品: {item['title']}")
        print(f"价格: {item['price']}")
        print(f"描述: {item['fullDescription'][:100]}...")
        print(f"图片数: {len(item['detailImages'])}")
"""

import json
import time
from typing import List, Dict, Optional, Union
from XianyuApis import XianyuApis
from utils.xianyu_utils import trans_cookies, generate_device_id


class XianyuClient:
    """闲鱼商品信息获取客户端"""
    
    def __init__(self, cookies_str: str):
        """
        初始化客户端
        
        Args:
            cookies_str: 闲鱼网站的Cookie字符串
        """
        self.cookies_str = cookies_str
        self.xianyu = XianyuApis()
        self.cookies = None
        self.device_id = None
        self.user_id = None
        
        # 初始化认证信息
        self._init_auth()
    
    def _init_auth(self):
        """初始化认证信息"""
        try:
            self.cookies = trans_cookies(self.cookies_str)
            self.user_id = self.cookies['unb']
            self.device_id = generate_device_id(self.user_id)
        except Exception as e:
            raise ValueError(f"Cookie解析失败: {e}")
    
    def get_items_list(self, 
                      seller_id: Optional[str] = None,
                      page_num: int = 1,
                      page_size: int = 20,
                      get_all_pages: bool = False) -> Dict:
        """
        获取商品列表
        
        Args:
            seller_id: 卖家ID，None表示获取自己的商品
            page_num: 页码，从1开始
            page_size: 每页商品数量，最大20
            get_all_pages: 是否自动获取所有页面
            
        Returns:
            包含商品列表的字典
        """
        all_items = []
        current_page = page_num
        
        while True:
            try:
                response = self.xianyu.get_seller_items(
                    cookies=self.cookies,
                    device_id=self.device_id,
                    seller_id=seller_id,
                    page_num=current_page,
                    page_size=page_size
                )
                
                result = self.xianyu.parse_seller_items(response)
                
                if not result['success']:
                    return {
                        'success': False,
                        'message': result['message'],
                        'data': {'items': all_items, 'total': len(all_items)}
                    }
                
                page_items = result['data']['items']
                if not page_items:
                    break
                
                all_items.extend(page_items)
                
                # 检查是否继续获取下一页
                if not get_all_pages or not result['data']['hasMore']:
                    break
                
                current_page += 1
                time.sleep(0.5)  # 避免请求过快
                
            except Exception as e:
                return {
                    'success': False,
                    'message': f"获取第{current_page}页失败: {str(e)}",
                    'data': {'items': all_items, 'total': len(all_items)}
                }
        
        return {
            'success': True,
            'message': '获取成功',
            'data': {
                'items': all_items,
                'total': len(all_items),
                'pages': current_page - page_num + 1
            }
        }
    
    def get_item_detail(self, item_id: str) -> Dict:
        """
        获取单个商品的详细信息
        
        Args:
            item_id: 商品ID
            
        Returns:
            商品详细信息字典
        """
        try:
            response = self.xianyu.get_item_detail(
                cookies=self.cookies,
                device_id=self.device_id,
                item_id=item_id
            )
            
            return self.xianyu.parse_item_detail(response)
            
        except Exception as e:
            return {
                'success': False,
                'message': f"获取商品详情失败: {str(e)}",
                'data': None
            }
    
    def get_all_items(self,
                     seller_id: Optional[str] = None,
                     get_details: bool = True,
                     max_items: int = 0,
                     delay: float = 1.0,
                     progress_callback: Optional[callable] = None) -> List[Dict]:
        """
        获取所有商品信息（包含详细信息）
        
        Args:
            seller_id: 卖家ID，None表示获取自己的商品
            get_details: 是否获取详细信息（完整描述和图片）
            max_items: 最大获取数量，0表示全部
            delay: 每个商品详情获取的延迟时间（秒）
            progress_callback: 进度回调函数，接收 (current, total, item) 参数
            
        Returns:
            商品信息列表
        """
        # 获取商品列表
        list_result = self.get_items_list(
            seller_id=seller_id,
            get_all_pages=True
        )
        
        if not list_result['success']:
            raise Exception(f"获取商品列表失败: {list_result['message']}")
        
        items = list_result['data']['items']
        if not items:
            return []
        
        # 限制获取数量
        if max_items > 0:
            items = items[:max_items]
        
        if not get_details:
            return items
        
        # 获取详细信息
        enhanced_items = []
        
        for i, item in enumerate(items, 1):
            try:
                # 调用进度回调
                if progress_callback:
                    progress_callback(i, len(items), item)
                
                # 获取详细信息
                detail_result = self.get_item_detail(item['itemId'])
                
                if detail_result['success']:
                    detail_data = detail_result['data']
                    
                    # 增强商品信息
                    enhanced_item = item.copy()
                    enhanced_item['fullDescription'] = detail_data['desc']
                    enhanced_item['detailImages'] = detail_data['images']
                    enhanced_item['hasDetailDesc'] = bool(detail_data['desc'])
                    enhanced_item['descImproved'] = bool(detail_data['desc']) and detail_data['desc'] != item['description']
                    
                    enhanced_items.append(enhanced_item)
                else:
                    # 详情获取失败，保留原始信息
                    enhanced_items.append(item)
                
                # 添加延迟
                if i < len(items):
                    time.sleep(delay)
                    
            except Exception as e:
                # 异常时保留原始信息
                enhanced_items.append(item)
        
        return enhanced_items
    
    def get_simple_items(self, 
                        seller_id: Optional[str] = None,
                        max_items: int = 0) -> List[Dict]:
        """
        快速获取商品基本信息（不包含详细描述）
        
        Args:
            seller_id: 卖家ID，None表示获取自己的商品
            max_items: 最大获取数量，0表示全部
            
        Returns:
            商品基本信息列表
        """
        return self.get_all_items(
            seller_id=seller_id,
            get_details=False,
            max_items=max_items
        )
    
    def search_items(self, 
                    keyword: str,
                    items: Optional[List[Dict]] = None) -> List[Dict]:
        """
        在商品列表中搜索关键词
        
        Args:
            keyword: 搜索关键词
            items: 商品列表，None表示获取所有商品后搜索
            
        Returns:
            匹配的商品列表
        """
        if items is None:
            items = self.get_simple_items()
        
        keyword = keyword.lower()
        matched_items = []
        
        for item in items:
            # 在标题和描述中搜索
            title = item.get('title', '').lower()
            desc = item.get('description', '').lower()
            full_desc = item.get('fullDescription', '').lower()
            
            if (keyword in title or 
                keyword in desc or 
                keyword in full_desc):
                matched_items.append(item)
        
        return matched_items
    
    def get_user_info(self) -> Dict:
        """获取当前用户信息"""
        return {
            'user_id': self.user_id,
            'device_id': self.device_id
        }


# 便捷函数
def create_client(cookies_str: str) -> XianyuClient:
    """创建闲鱼客户端的便捷函数"""
    return XianyuClient(cookies_str)


def get_items_quick(cookies_str: str, 
                   max_items: int = 10,
                   get_details: bool = True) -> List[Dict]:
    """
    快速获取商品信息的便捷函数
    
    Args:
        cookies_str: Cookie字符串
        max_items: 最大获取数量
        get_details: 是否获取详细信息
        
    Returns:
        商品信息列表
    """
    client = XianyuClient(cookies_str)
    return client.get_all_items(max_items=max_items, get_details=get_details)


if __name__ == '__main__':
    # 测试代码
    cookies_str = r"your_cookies_here"
    
    try:
        client = XianyuClient(cookies_str)
        print(f"用户信息: {client.get_user_info()}")
        
        # 获取前5个商品的详细信息
        items = client.get_all_items(max_items=5, get_details=True)
        
        for i, item in enumerate(items, 1):
            print(f"\n商品 {i}:")
            print(f"  标题: {item['title']}")
            print(f"  价格: {item['price']}")
            print(f"  描述长度: {len(item.get('fullDescription', ''))}")
            print(f"  图片数量: {len(item.get('detailImages', []))}")
            
    except Exception as e:
        print(f"错误: {e}")

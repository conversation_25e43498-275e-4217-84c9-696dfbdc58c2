const CardKey = require('./CardKey');
const Category = require('./Category');
const File = require('./File');
const Admin = require('./Admin');
const OperationLog = require('./OperationLog');

// 定义模型之间的关联关系
// File 和 Category 的关联关系已在 File 模型中定义

// CardKey 和 File 的关联关系
CardKey.belongsTo(File, { foreignKey: 'used_file_id', as: 'usedFile' });
File.hasMany(CardKey, { foreignKey: 'used_file_id', as: 'cardKeys' });

// OperationLog 和 File 的关联关系
OperationLog.belongsTo(File, { foreignKey: 'file_id', as: 'file' });
File.hasMany(OperationLog, { foreignKey: 'file_id', as: 'operationLogs' });

module.exports = {
  CardKey,
  Category,
  File,
  Admin,
  OperationLog
}; 
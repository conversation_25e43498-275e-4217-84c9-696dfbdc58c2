import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getFileDetails, extractFile, clearFileError, clearCurrentFile } from '../store/slices/fileSlice';
import {
  Card,
  Button,
  Typography,
  Spin,
  Descriptions,
  Divider,
  Alert,
  Result,
  Space,
  Tag,
  Modal,
  message,
  Image
} from 'antd';
import {
  ArrowLeftOutlined,
  FileOutlined,
  FileTextOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileZipOutlined,
  CloudDownloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;

// 用户端图片组件，支持身份验证
const UserAuthenticatedImage = ({ src, ...props }) => {
  const [imageSrc, setImageSrc] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      try {
        const userToken = localStorage.getItem('userToken');
        const response = await axios.get(src, {
          headers: userToken ? {
            'Authorization': `Bearer ${userToken}`
          } : {},
          responseType: 'blob'
        });

        const imageUrl = URL.createObjectURL(response.data);
        setImageSrc(imageUrl);
        setLoading(false);
      } catch (error) {
        console.error('加载图片失败:', error);
        setLoading(false);
      }
    };

    if (src) {
      loadImage();
    }

    return () => {
      if (imageSrc) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [src, imageSrc]);

  if (loading) {
    return <div style={{ width: props.width || 120, height: props.height || 120, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px solid #eee', borderRadius: 4 }}>
      <Spin size="small" />
    </div>;
  }

  if (!imageSrc) {
    return <div style={{ width: props.width || 120, height: props.height || 120, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px solid #eee', borderRadius: 4, background: '#f5f5f5' }}>
      <span style={{ color: '#999', fontSize: 12 }}>加载失败</span>
    </div>;
  }

  return <img src={imageSrc} {...props} alt="" />;
};

// 文件图标映射
const getFileIcon = (fileType) => {
  // 特殊处理纯文本类型
  if (fileType === 'text') {
    return <FileTextOutlined style={{ fontSize: 56, color: '#1890ff' }} />;
  }

  switch (fileType?.toLowerCase()) {
    case 'pdf':
      return <FilePdfOutlined style={{ fontSize: 56, color: '#ff4d4f' }} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
      return <FileImageOutlined style={{ fontSize: 56, color: '#52c41a' }} />;
    case 'doc':
    case 'docx':
    case 'txt':
      return <FileTextOutlined style={{ fontSize: 56, color: '#1890ff' }} />;
    case 'zip':
    case 'rar':
    case '7z':
      return <FileZipOutlined style={{ fontSize: 56, color: '#faad14' }} />;
    default:
      return <FileOutlined style={{ fontSize: 56, color: '#722ed1' }} />;
  }
};

// 格式化文件大小
const formatFileSize = (sizeInKB) => {
  // 如果文件大小为null或0（网盘链接类资料），不显示大小
  if (sizeInKB === null || sizeInKB === 0) {
    return '';
  }
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} MB`;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
};

const FileDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentFile, downloadUrl, loadingDetails, extracting, error } = useSelector((state) => state.file);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [downloading, setDownloading] = useState(false);
  
  // 打印调试信息
  useEffect(() => {
    console.log('当前文件对象:', currentFile);
    console.log('下载URL:', downloadUrl);
  }, [currentFile, downloadUrl]);

  // 加载文件详情
  useEffect(() => {
    if (id) {
      dispatch(getFileDetails(id));
    }
    
    // 组件卸载时清除当前文件数据
    return () => {
      dispatch(clearCurrentFile());
    };
  }, [id, dispatch]);

  // 处理返回
  const handleBack = () => {
    navigate('/files');
  };

  // 处理提取文件
  const handleExtract = () => {
    setIsModalOpen(true);
  };

  // 确认提取
  const handleConfirmExtract = () => {
    dispatch(extractFile(id));
    setIsModalOpen(false);
  };

  // 取消提取
  const handleCancelExtract = () => {
    setIsModalOpen(false);
  };

  // 处理下载
  const handleDownload = async () => {
    if (downloadUrl) {
      try {
        setDownloading(true);
        
        // 使用axios直接请求文件并触发浏览器下载
        const response = await axios({
          url: downloadUrl,
          method: 'GET',
          responseType: 'blob', // 重要: 表示服务器将返回二进制文件
        });
        
        // 创建blob链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        
        // 创建下载链接并触发点击
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', currentFile?.file_name || 'download');
        document.body.appendChild(link);
        link.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
        setDownloading(false);
      } catch (error) {
        console.error('下载文件失败:', error);
        setDownloading(false);
        message.error('下载文件失败，请稍后重试');
      }
    }
  };

  if (loadingDetails) {
    return <div style={{ textAlign: 'center', padding: '50px 0' }}><Spin size="large" /></div>;
  }

  if (!currentFile && !loadingDetails) {
    return (
      <Result
        status="404"
        title="文件不存在"
        subTitle="抱歉，您访问的文件不存在或已被移除。"
        extra={
          <Button type="primary" onClick={handleBack}>
            返回文件列表
          </Button>
        }
      />
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 20 }}>
        <Button type="link" icon={<ArrowLeftOutlined />} onClick={handleBack}>
          返回文件列表
        </Button>
      </div>

      {error && (
        <Alert
          message="操作失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 20 }}
          closable
          onClose={() => dispatch(clearFileError())}
        />
      )}

      <Modal
        title="确认提取文件"
        open={isModalOpen}
        onOk={handleConfirmExtract}
        onCancel={handleCancelExtract}
        okText="确认"
        cancelText="取消"
      >
        <div>
          <p>您确定要提取此文件吗？</p>
          <p style={{ color: '#ff4d4f' }}>注意：每个卡密只能提取一个文件，提取后无法更换！</p>
        </div>
      </Modal>

      <Card>
        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
          <div style={{ marginRight: 20, textAlign: 'center' }}>
            {currentFile && getFileIcon(currentFile.file_type)}
            {currentFile && (
              <Tag color="blue" style={{ marginTop: 8 }}>
                {currentFile.file_type.toUpperCase()}
              </Tag>
            )}
          </div>

          <div style={{ flex: 1 }}>
            <Title level={3}>{currentFile?.file_name}</Title>
            
            {downloadUrl ? (
              <Alert
                message="提取成功"
                description={currentFile?.file_type === 'text' ? 
                  "网盘链接已成功提取，请在下方查看内容。" : 
                  "文件已成功提取，请点击下方按钮下载文件。"}
                type="success"
                showIcon
                style={{ marginBottom: 20 }}
              />
            ) : null}

            <Title level={4} style={{ marginTop: 24, marginBottom: 16 }}>文件信息</Title>
            
            <div style={{ display: 'flex', flexWrap: 'wrap', marginBottom: 8 }}>
              {currentFile?.file_type === 'text' ? (
                // 网盘链接文件的布局 - 所属分类放在上传时间右边
                <>
                  {/* 对于text类型文件，不显示文件大小，直接从上传时间和所属分类开始 */}
                  <div style={{ display: 'flex', flexWrap: 'nowrap', width: '100%', marginBottom: 16, alignItems: 'center' }}>
                    <div style={{ marginRight: 32 }}>
                    <span style={{ color: 'rgba(0, 0, 0, 0.45)', marginRight: 8 }}>上传时间:</span>
                    <span>{currentFile ? formatDate(currentFile.upload_time) : ''}</span>
                  </div>
                    <div style={{ marginRight: 32 }}>
                    <span style={{ color: 'rgba(0, 0, 0, 0.45)', marginRight: 8 }}>所属分类:</span>
                    <span>{currentFile?.Category?.name || '未分类'}</span>
                    </div>
                    <div>
                      <span style={{ color: 'rgba(0, 0, 0, 0.45)', marginRight: 8 }}>提取次数:</span>
                      <span>{currentFile?.download_count || 0} 次</span>
                    </div>
                  </div>
                </>
              ) : (
                // 其他文件类型的原有布局
                <>
                  <div style={{ display: 'flex', flexWrap: 'nowrap', width: '100%', marginBottom: 16, alignItems: 'center' }}>
                  {currentFile?.file_size && (
                      <div style={{ marginRight: 24 }}>
                      <span style={{ color: 'rgba(0, 0, 0, 0.45)', marginRight: 8 }}>文件大小:</span>
                      <span>{formatFileSize(currentFile.file_size)}</span>
                    </div>
                  )}
                    
                    <div style={{ marginRight: 24 }}>
                    <span style={{ color: 'rgba(0, 0, 0, 0.45)', marginRight: 8 }}>上传时间:</span>
                    <span>{currentFile ? formatDate(currentFile.upload_time) : ''}</span>
                  </div>
                    
                    {currentFile?.file_type !== 'text' && (
                      <div style={{ marginRight: 24 }}>
                        <span style={{ color: 'rgba(0, 0, 0, 0.45)', marginRight: 8 }}>所属分类:</span>
                        <span>{currentFile?.Category?.name || '未分类'}</span>
                      </div>
                    )}
                    
                  {currentFile?.file_type !== 'text' && (
                      <div>
                      <span style={{ color: 'rgba(0, 0, 0, 0.45)', marginRight: 8 }}>下载次数:</span>
                      <span>{currentFile?.download_count || 0} 次</span>
                    </div>
                  )}
                  </div>
                </>
              )}
            </div>

            <Title level={4} style={{ marginTop: 0, marginBottom: 16 }}>文件描述</Title>

            <Paragraph style={{ whiteSpace: 'pre-wrap', padding: 0 }}>
              {currentFile?.description || '暂无描述信息'}
            </Paragraph>

            {/* 简介图片显示 */}
            {currentFile?.preview_images_parsed && currentFile.preview_images_parsed.length > 0 && (
              <>
                <Title level={4} style={{ marginTop: 24, marginBottom: 16 }}>简介图片</Title>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginBottom: 20 }}>
                  {currentFile.preview_images_parsed.map((imagePath, index) => (
                    <UserAuthenticatedImage
                      key={index}
                      width={120}
                      height={120}
                      src={`/api/files/preview/${imagePath}`}
                      style={{ objectFit: 'cover', borderRadius: 4, border: '1px solid #eee', width: 120, height: 120 }}
                    />
                  ))}
                </div>
              </>
            )}

            {/* 网盘链接 - 修复逻辑 */}
            {currentFile?.file_type === 'text' && downloadUrl && (
              <>
                <Title level={4} style={{ marginTop: 24, marginBottom: 16 }}>网盘链接</Title>
                <Paragraph style={{ whiteSpace: 'pre-wrap', padding: 0 }}>
                  {currentFile?.important_text ? currentFile.important_text : '无内容'}
                </Paragraph>
              </>
            )}

            <Divider />
            
            <div>
              {downloadUrl ? (
                currentFile?.file_type === 'text' ? (
                  // 已提取的网盘链接不显示下载按钮
                  null
                ) : (
                  // 普通文件显示下载按钮
                  <Button 
                    type="primary" 
                    size="large" 
                    icon={<CloudDownloadOutlined />}
                    onClick={handleDownload}
                    loading={downloading}
                  >
                    下载文件
                  </Button>
                )
              ) : (
                <Button 
                  type="primary" 
                  size="large" 
                  loading={extracting}
                  onClick={handleExtract}
                >
                  确认提取{currentFile?.file_type === 'text' ? '此网盘链接' : '此文件'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default FileDetails; 
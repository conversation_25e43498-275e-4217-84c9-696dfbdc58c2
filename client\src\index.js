import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import App from './App';
import { store } from './store';
import axios from 'axios';
import './index.css';

// 设置 Day.js 本地化配置
dayjs.locale('zh-cn');

// 配置 Axios 默认值
axios.defaults.baseURL = process.env.REACT_APP_API_URL || '';
axios.defaults.headers.common['Content-Type'] = 'application/json';

// 从本地存储中获取 token
const userToken = localStorage.getItem('userToken');
const adminToken = localStorage.getItem('adminToken');

// 如果有 token 则设置请求头
if (userToken) {
  axios.defaults.headers.common['Authorization'] = `Bearer ${userToken}`;
}

// 配置主题
const theme = {
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 2,
  },
};

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <ConfigProvider locale={zhCN} theme={theme}>
        <App />
      </ConfigProvider>
    </Provider>
  </React.StrictMode>
); 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼客户端使用示例

展示如何在不同项目中使用 XianyuClient 模块
"""

import json
from xianyu_client import XianyuClient, get_items_quick


# 示例1：基本使用
def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("示例1：基本使用")
    print("=" * 60)
    
    # 您的Cookie字符串
    cookies_str = r"your_cookies_here"
    
    try:
        # 创建客户端
        client = XianyuClient(cookies_str)
        
        # 获取用户信息
        user_info = client.get_user_info()
        print(f"用户ID: {user_info['user_id']}")
        
        # 获取前3个商品的详细信息
        items = client.get_all_items(max_items=3, get_details=True)
        
        for i, item in enumerate(items, 1):
            print(f"\n商品 {i}:")
            print(f"  ID: {item['itemId']}")
            print(f"  标题: {item['title']}")
            print(f"  价格: {item['price']}")
            print(f"  完整描述长度: {len(item.get('fullDescription', ''))}")
            print(f"  图片数量: {len(item.get('detailImages', []))}")
            
    except Exception as e:
        print(f"错误: {e}")


# 示例2：快速获取（便捷函数）
def example_quick_get():
    """使用便捷函数快速获取"""
    print("\n" + "=" * 60)
    print("示例2：快速获取")
    print("=" * 60)
    
    cookies_str = r"your_cookies_here"
    
    try:
        # 使用便捷函数快速获取
        items = get_items_quick(
            cookies_str=cookies_str,
            max_items=5,
            get_details=True
        )
        
        print(f"获取到 {len(items)} 个商品")
        
        for item in items:
            print(f"- {item['title']} (¥{item['price']})")
            
    except Exception as e:
        print(f"错误: {e}")


# 示例3：只获取基本信息（快速）
def example_simple_items():
    """只获取基本信息，不获取详细描述"""
    print("\n" + "=" * 60)
    print("示例3：快速获取基本信息")
    print("=" * 60)
    
    cookies_str = r"your_cookies_here"
    
    try:
        client = XianyuClient(cookies_str)
        
        # 快速获取基本信息（不调用详情API）
        items = client.get_simple_items(max_items=10)
        
        print(f"快速获取到 {len(items)} 个商品的基本信息")
        
        for item in items:
            print(f"- {item['title']} (¥{item['price']}) - {item['description'][:50]}...")
            
    except Exception as e:
        print(f"错误: {e}")


# 示例4：搜索功能
def example_search():
    """搜索商品"""
    print("\n" + "=" * 60)
    print("示例4：搜索功能")
    print("=" * 60)
    
    cookies_str = r"your_cookies_here"
    
    try:
        client = XianyuClient(cookies_str)
        
        # 搜索包含"考研"的商品
        matched_items = client.search_items("考研")
        
        print(f"找到 {len(matched_items)} 个包含'考研'的商品:")
        
        for item in matched_items:
            print(f"- {item['title']} (¥{item['price']})")
            
    except Exception as e:
        print(f"错误: {e}")


# 示例5：带进度回调
def example_with_progress():
    """带进度显示的获取"""
    print("\n" + "=" * 60)
    print("示例5：带进度显示")
    print("=" * 60)
    
    cookies_str = r"your_cookies_here"
    
    def progress_callback(current, total, item):
        """进度回调函数"""
        print(f"进度: {current}/{total} - 正在处理: {item['title'][:30]}...")
    
    try:
        client = XianyuClient(cookies_str)
        
        # 获取详细信息，带进度显示
        items = client.get_all_items(
            max_items=5,
            get_details=True,
            delay=1.0,
            progress_callback=progress_callback
        )
        
        print(f"\n完成！获取到 {len(items)} 个商品的详细信息")
        
    except Exception as e:
        print(f"错误: {e}")


# 示例6：集成到Web应用（Flask示例）
def example_flask_integration():
    """Flask Web应用集成示例"""
    print("\n" + "=" * 60)
    print("示例6：Flask Web应用集成")
    print("=" * 60)
    
    # 这是一个Flask应用的示例代码
    flask_code = '''
from flask import Flask, jsonify, request
from xianyu_client import XianyuClient

app = Flask(__name__)

@app.route('/api/items')
def get_items():
    """获取商品列表API"""
    try:
        # 从请求中获取参数
        cookies = request.headers.get('X-Xianyu-Cookies')
        max_items = int(request.args.get('max_items', 10))
        get_details = request.args.get('get_details', 'true').lower() == 'true'
        
        if not cookies:
            return jsonify({'error': 'Missing cookies'}), 400
        
        # 创建客户端并获取商品
        client = XianyuClient(cookies)
        items = client.get_all_items(
            max_items=max_items,
            get_details=get_details
        )
        
        return jsonify({
            'success': True,
            'data': items,
            'count': len(items)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search')
def search_items():
    """搜索商品API"""
    try:
        cookies = request.headers.get('X-Xianyu-Cookies')
        keyword = request.args.get('keyword', '')
        
        if not cookies or not keyword:
            return jsonify({'error': 'Missing cookies or keyword'}), 400
        
        client = XianyuClient(cookies)
        matched_items = client.search_items(keyword)
        
        return jsonify({
            'success': True,
            'data': matched_items,
            'count': len(matched_items)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
'''
    
    print("Flask集成代码:")
    print(flask_code)


# 示例7：数据分析应用
def example_data_analysis():
    """数据分析应用示例"""
    print("\n" + "=" * 60)
    print("示例7：数据分析应用")
    print("=" * 60)
    
    cookies_str = r"your_cookies_here"
    
    try:
        client = XianyuClient(cookies_str)
        
        # 获取所有商品的基本信息
        items = client.get_simple_items()
        
        if not items:
            print("没有商品数据")
            return
        
        # 价格分析
        prices = []
        for item in items:
            try:
                price = float(item['price'].replace('¥', '').replace(',', ''))
                prices.append(price)
            except:
                pass
        
        if prices:
            print(f"商品数量: {len(items)}")
            print(f"价格统计:")
            print(f"  最低价: ¥{min(prices):.2f}")
            print(f"  最高价: ¥{max(prices):.2f}")
            print(f"  平均价: ¥{sum(prices)/len(prices):.2f}")
        
        # 分类统计
        categories = {}
        for item in items:
            category = item.get('category', '未分类')
            categories[category] = categories.get(category, 0) + 1
        
        print(f"\n分类统计:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"  {category}: {count} 个商品")
            
    except Exception as e:
        print(f"错误: {e}")


# 示例8：保存到数据库
def example_save_to_database():
    """保存到数据库示例"""
    print("\n" + "=" * 60)
    print("示例8：保存到数据库")
    print("=" * 60)
    
    # SQLite示例代码
    sqlite_code = '''
import sqlite3
from xianyu_client import XianyuClient

def save_items_to_db(cookies_str, db_path='xianyu_items.db'):
    """将商品信息保存到SQLite数据库"""
    
    # 创建数据库连接
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS items (
            id TEXT PRIMARY KEY,
            title TEXT,
            price TEXT,
            description TEXT,
            full_description TEXT,
            image_count INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    try:
        # 获取商品信息
        client = XianyuClient(cookies_str)
        items = client.get_all_items(get_details=True)
        
        # 保存到数据库
        for item in items:
            cursor.execute("""
                INSERT OR REPLACE INTO items 
                (id, title, price, description, full_description, image_count)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                item['itemId'],
                item['title'],
                item['price'],
                item.get('description', ''),
                item.get('fullDescription', ''),
                len(item.get('detailImages', []))
            ))
        
        conn.commit()
        print(f"成功保存 {len(items)} 个商品到数据库")
        
    except Exception as e:
        print(f"保存失败: {e}")
    finally:
        conn.close()

# 使用示例
# save_items_to_db("your_cookies_here")
'''
    
    print("SQLite数据库保存代码:")
    print(sqlite_code)


if __name__ == '__main__':
    # 运行所有示例
    print("闲鱼客户端使用示例")
    print("注意：请先在代码中设置正确的cookies_str")
    
    # 取消注释以运行特定示例
    # example_basic_usage()
    # example_quick_get()
    # example_simple_items()
    # example_search()
    # example_with_progress()
    example_flask_integration()
    # example_data_analysis()
    example_save_to_database()

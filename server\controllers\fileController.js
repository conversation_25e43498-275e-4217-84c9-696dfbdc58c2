const path = require('path');
const fs = require('fs');
const { File, Category, CardKey } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 获取文件列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getFiles = async (req, res) => {
  try {
    const { category_id, keyword, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const where = { status: true };
    if (category_id) {
      where.category_id = category_id;
    }
    if (keyword) {
      where.file_name = { [Op.like]: `%${keyword}%` };
    }

    // 获取文件列表，按提取次数排序
    const { count, rows } = await File.findAndCountAll({
      where,
      include: [
        {
          model: Category,
          attributes: ['id', 'name']
        }
      ],
      attributes: [
        'id',
        'file_name',
        'file_type',
        'file_size',
        'description',
        'upload_time',
        // 添加提取次数统计
        [
          sequelize.literal(`(
            SELECT COUNT(*)
            FROM operation_logs
            WHERE operation_logs.file_id = File.id
            AND operation_logs.operation_type = '提取文件'
          )`),
          'extraction_count'
        ]
      ],
      order: [
        // 首先按提取次数降序排列
        [sequelize.literal('extraction_count'), 'DESC'],
        // 然后按上传时间降序排列
        ['upload_time', 'DESC']
      ],
      offset,
      limit: parseInt(limit)
    });

    return res.json({
      success: true,
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        files: rows
      }
    });
  } catch (error) {
    console.error('获取文件列表错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取文件详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getFileDetails = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await File.findOne({
      where: { id, status: true },
      include: [{ model: Category, attributes: ['id', 'name'] }],
      attributes: [
        'id', 'file_name', 'file_type', 'file_size', 'description',
        'upload_time', 'download_count', 'important_text'
      ]
    });

    if (!file) {
      return res.status(404).json({ success: false, message: '文件不存在或已下架' });
    }

    return res.json({
      success: true,
      data: { file }
    });
  } catch (error) {
    console.error('获取文件详情错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 提取文件（标记卡密为已使用）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const extractFile = async (req, res) => {
  try {
    const { fileId } = req.params;
    const { id: cardKeyId, key_code, device } = req.user;

    // 查询文件
    const file = await File.findOne({
      where: { id: fileId, status: true }
    });

    if (!file) {
      return res.status(404).json({ success: false, message: '文件不存在或已下架' });
    }

    // 查询卡密
    const cardKey = await CardKey.findByPk(cardKeyId);
    
    if (!cardKey) {
      return res.status(404).json({ success: false, message: '卡密不存在' });
    }

    // 处理单设备类型卡密
    if (cardKey.key_type === 'single') {
      if (cardKey.is_used) {
        // 检查之前用过这个卡密的是不是当前设备
        if (cardKey.used_devices) {
          try {
            const usedDevices = JSON.parse(cardKey.used_devices);
            if (!usedDevices.includes(device)) {
              return res.status(400).json({ success: false, message: '卡密已被使用' });
            }
            
            // 检查当前设备是否已经提取过文件
            if (cardKey.used_file_id) {
              return res.status(400).json({ success: false, message: '该设备已提取过文件' });
            }
          } catch (err) {
            console.error('解析已使用设备列表错误:', err);
            return res.status(400).json({ success: false, message: '卡密已被使用' });
          }
        } else {
          return res.status(400).json({ success: false, message: '卡密已被使用' });
        }
      }

      // 标记卡密为已使用并记录文件ID
      await cardKey.update({
        is_used: true,
        used_time: new Date(),
        used_file_id: fileId,
        used_devices: cardKey.used_devices || JSON.stringify([device])
      });
    } 
    // 处理多设备类型卡密
    else if (cardKey.key_type === 'multi') {
      // 解析已使用设备列表
      let usedDevices = [];
      if (cardKey.used_devices) {
        try {
          usedDevices = JSON.parse(cardKey.used_devices);
          // 标准化设备列表，确保设备ID格式一致
          const devicesList = usedDevices.map(entry => 
            typeof entry === 'object' ? entry.device : entry
          );
          
          // 检查当前设备是否已经在设备列表中
          if (devicesList.includes(device)) {
            // 检查当前设备是否已经提取过文件
            const deviceHasFile = usedDevices.some(entry => 
              typeof entry === 'object' && entry.device === device && entry.fileId);
              
            if (deviceHasFile) {
              return res.status(400).json({ success: false, message: '该设备已提取过文件' });
            }
            
            // 如果在设备列表中但没有提取过文件，则更新设备信息
            usedDevices = usedDevices.map(entry => {
              // 如果是字符串格式（兼容旧数据），转换为对象格式
              if (typeof entry === 'string' && entry === device) {
                return { device, fileId };
              } 
              // 如果是对象格式且匹配当前设备，更新文件ID
              else if (typeof entry === 'object' && entry.device === device) {
                return { ...entry, fileId };
              }
              // 其他情况保持不变
              return entry;
            });
          } else {
            // 检查是否超出最大设备数
            if (devicesList.length >= cardKey.max_devices) {
              return res.status(400).json({ success: false, message: '卡密已达到最大使用设备数' });
            }
            
            // 添加新设备记录
            usedDevices.push({ device, fileId });
          }
          
          // 更新卡密记录
          await cardKey.update({
            used_devices: JSON.stringify(usedDevices),
            used_time: new Date(),
            used_file_id: fileId,  // 记录最后一次提取的文件ID
            // 如果达到最大设备数，则标记为已使用
            is_used: devicesList.length + 1 >= cardKey.max_devices
          });
        } catch (err) {
          console.error('解析已使用设备列表错误:', err);
          return res.status(500).json({ success: false, message: '服务器错误' });
        }
      } else {
        // 首次使用卡密，记录设备和文件
        await cardKey.update({
          used_devices: JSON.stringify([{ device, fileId }]),
          used_time: new Date(),
          used_file_id: fileId,
          is_used: 1 >= cardKey.max_devices
        });
      }
    }

    // 增加文件下载计数
    await file.increment('download_count');

    // 生成下载链接（有效期10分钟）
    const timestamp = Date.now();
    const signature = `${fileId}_${timestamp}_${key_code.substring(0, 8)}`;
    const downloadUrl = `/api/download/${fileId}?timestamp=${timestamp}&signature=${signature}`;

    // 重新获取文件信息（包含文件的最新下载计数）
    const updatedFile = await File.findOne({
      where: { id: fileId, status: true },
      include: [{ model: Category, attributes: ['id', 'name'] }],
      attributes: [
        'id', 'file_name', 'file_type', 'file_size', 'description',
        'upload_time', 'download_count', 'important_text'
      ]
    });

    console.log('提取文件-文件详情:', JSON.stringify(updatedFile));
    console.log('提取文件-网盘链接:', updatedFile.important_text);

    // 将卡密信息附加到请求对象，以便日志中间件可以获取到
    req.extractedCardKey = cardKey.key_code;

    return res.json({
      success: true,
      message: '提取成功',
      data: {
        downloadUrl,
        file: updatedFile,
        cardKey: cardKey.key_code // 在响应中也包含卡密信息
      }
    });
  } catch (error) {
    console.error('提取文件错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 下载文件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const downloadFile = async (req, res) => {
  try {
    const { fileId } = req.params;
    const { timestamp, signature } = req.query;

    // 简单验证下载链接
    if (!timestamp || !signature) {
      return res.status(400).json({ success: false, message: '无效的下载链接' });
    }

    // 验证链接是否过期（10分钟有效期）
    const now = Date.now();
    if (now - parseInt(timestamp) > 10 * 60 * 1000) {
      return res.status(400).json({ success: false, message: '下载链接已过期' });
    }

    // 查询文件
    const file = await File.findOne({
      where: { id: fileId, status: true }
    });

    if (!file) {
      return res.status(404).json({ success: false, message: '文件不存在或已下架' });
    }

    // 设置上传路径为当前服务器目录下的uploads文件夹
    const baseUploadPath = path.join(__dirname, '../uploads');
    let filePath;
    
    // 调试信息
    console.log('文件ID:', fileId);
    console.log('文件名称:', file.file_name);
    console.log('文件类型:', file.file_type);
    console.log('文件路径:', file.file_path);
    console.log('基础上传路径:', baseUploadPath);
    
    // 处理文件路径
    if (file.file_path.includes(path.sep)) {
      // 包含路径分隔符的情况
      filePath = path.resolve(baseUploadPath, file.file_path);
    } else {
      // 根据文件类型确定子文件夹
      let subFolder = 'other';
      
      switch(file.file_type.toLowerCase()) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
          subFolder = 'images';
          break;
        case 'pdf':
          subFolder = 'pdf';
          break;
        case 'doc':
        case 'docx':
          subFolder = 'word';
          break;
        case 'xls':
        case 'xlsx':
          subFolder = 'excel';
          break;
        case 'zip':
        case 'rar':
        case '7z':
          subFolder = 'archives';
          break;
      }
      
      // 先尝试在对应类型文件夹中查找
      filePath = path.resolve(baseUploadPath, subFolder, file.file_path);
      console.log('尝试路径1:', filePath);
      
      // 如果不存在，再尝试在根上传目录查找
      if (!fs.existsSync(filePath)) {
        filePath = path.resolve(baseUploadPath, file.file_path);
        console.log('尝试路径2:', filePath);
        
        // 仍不存在，尝试在其他类型文件夹中查找
        if (!fs.existsSync(filePath)) {
          const typeFolders = ['images', 'word', 'pdf', 'excel', 'audio', 'video', 'archives', 'code', 'other'];
          
          for (const folder of typeFolders) {
            const possiblePath = path.resolve(baseUploadPath, folder, file.file_path);
            console.log('尝试路径:', possiblePath);
            if (fs.existsSync(possiblePath)) {
              filePath = possiblePath;
              console.log('文件找到于:', filePath);
              break;
            }
          }
        }
      }
    }

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.error(`文件不存在: ${filePath}`);
      return res.status(404).json({ success: false, message: '文件不存在' });
    }

    // 记录下载信息
    console.log(`正在下载文件: ${filePath}, 原始文件名: ${file.file_name}`);

    // 设置响应头
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.file_name)}"`);

    // 创建可读流并发送文件
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('下载文件错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 直接下载文件（用于管理员预览）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const directDownload = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询文件
    const file = await File.findOne({
      where: { id }
    });

    if (!file) {
      return res.status(404).json({ success: false, message: '文件不存在' });
    }

    // 如果是纯文本记录没有物理文件，则返回文本内容
    if (file.file_type === 'text' && !file.file_path) {
      return res.json({
        success: true,
        data: {
          isText: true,
          content: file.important_text || ''
        }
      });
    }

    // 设置上传路径为当前服务器目录下的uploads文件夹
    const baseUploadPath = path.join(__dirname, '../uploads');
    let filePath;
    
    // 处理文件路径
    if (file.file_path && file.file_path.includes(path.sep)) {
      // 包含路径分隔符的情况
      filePath = path.resolve(baseUploadPath, file.file_path);
    } else if (file.file_path) {
      // 根据文件类型确定子文件夹
      let subFolder = 'other';
      
      switch(file.file_type.toLowerCase()) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
          subFolder = 'images';
          break;
        case 'pdf':
          subFolder = 'pdf';
          break;
        case 'doc':
        case 'docx':
          subFolder = 'word';
          break;
        case 'xls':
        case 'xlsx':
          subFolder = 'excel';
          break;
        case 'zip':
        case 'rar':
        case '7z':
          subFolder = 'archives';
          break;
      }
      
      // 先尝试在对应类型文件夹中查找
      filePath = path.resolve(baseUploadPath, subFolder, file.file_path);
      
      // 如果不存在，再尝试在根上传目录查找
      if (!fs.existsSync(filePath)) {
        filePath = path.resolve(baseUploadPath, file.file_path);
        
        // 仍不存在，尝试在其他类型文件夹中查找
        if (!fs.existsSync(filePath)) {
          const typeFolders = ['images', 'word', 'pdf', 'excel', 'audio', 'video', 'archives', 'code', 'other'];
          
          for (const folder of typeFolders) {
            const possiblePath = path.resolve(baseUploadPath, folder, file.file_path);
            if (fs.existsSync(possiblePath)) {
              filePath = possiblePath;
              break;
            }
          }
        }
      }
    } else {
      return res.status(404).json({ success: false, message: '文件路径不存在' });
    }

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.error(`文件不存在: ${filePath}`);
      return res.status(404).json({ success: false, message: '文件不存在' });
    }

    // 根据文件类型设置适当的Content-Type
    let contentType = 'application/octet-stream';
    switch(file.file_type.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'gif':
        contentType = 'image/gif';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        break;
      case 'mp4':
        contentType = 'video/mp4';
        break;
      case 'webm':
        contentType = 'video/webm';
        break;
    }

    // 根据Content-Type决定是在浏览器中预览还是下载
    if (req.query.download === 'true') {
      // 强制下载模式
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.file_name)}"`);
    } else {
      // 预览模式
      res.setHeader('Content-Type', contentType);
      // 对于图片、PDF等可以预览的文件类型，浏览器会尝试在浏览器中打开
    }

    // 创建可读流并发送文件
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('直接下载文件错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

module.exports = {
  getFiles,
  getFileDetails,
  extractFile,
  downloadFile,
  directDownload
}; 